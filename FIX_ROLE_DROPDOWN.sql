-- FIX ROLE DROPDOWN IN SUPABASE
-- This will create proper role constraints so you get a dropdown instead of text input
-- Run this ENTIRE script in Supabase SQL Editor

-- Step 1: Create an ENUM type for user roles
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('user', 'writer', 'editor', 'admin');
    END IF;
END $$;

-- Step 2: Add a check constraint to the existing role column
-- First, let's see what we have
SELECT 'Current role column info:' as status;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_profiles' AND column_name = 'role';

-- Step 3: Add check constraint to limit role values
ALTER TABLE user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_role_check;

ALTER TABLE user_profiles 
ADD CONSTRAINT user_profiles_role_check 
CHECK (role IN ('user', 'writer', 'editor', 'admin'));

-- Step 4: Update your specific profile to admin
UPDATE user_profiles 
SET role = 'admin' 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 5: Verify the constraint is working
SELECT 'Testing role constraint...' as status;

-- This should work (valid role)
UPDATE user_profiles 
SET role = 'editor' 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Change back to admin
UPDATE user_profiles 
SET role = 'admin' 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 6: Create a comment on the role column to help Supabase UI
COMMENT ON COLUMN user_profiles.role IS 'User role: user, writer, editor, or admin';

-- Step 7: Verify your profile
SELECT 'Your Profile:' as status;
SELECT 
  id,
  username,
  full_name,
  role,
  created_at,
  CASE 
    WHEN role IN ('admin', 'editor') THEN '✅ HAS ADMIN ACCESS' 
    ELSE '❌ NO ADMIN ACCESS' 
  END as admin_status
FROM user_profiles 
WHERE id = '8973ff45-fcf5-4b50-93f8-4068653efda0';

-- Step 8: Show all valid role options
SELECT 'Valid Role Options:' as info;
SELECT unnest(enum_range(NULL::user_role)) as valid_roles;

-- Success message
SELECT '✅ ROLE CONSTRAINT ADDED!' as result;
SELECT 'Now refresh Supabase dashboard and the role field should show a dropdown' as instruction;
SELECT 'Valid options: user, writer, editor, admin' as options;

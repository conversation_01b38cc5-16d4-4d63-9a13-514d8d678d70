# 🗄️ Complete Database Setup Guide - THE SACH PATRA

## 📋 Overview
This guide provides a complete database schema setup for your news website from scratch, including the role dropdown functionality you requested.

## 🎯 What You Get

### ✅ Core Features
- **User Management** with role-based permissions (guest, user, writer, editor, admin)
- **Article Management** with categories, tags, and SEO
- **Comment System** with moderation
- **Newsletter Management** with templates and campaigns
- **Advertisement System** with analytics
- **Media File Management**
- **Website Settings** management
- **Analytics Tracking**

### 🔧 Technical Features
- **Role Dropdown** in Supabase (exactly like your screenshot)
- **Row Level Security** for data protection
- **Automatic Triggers** for timestamps and slugs
- **Performance Indexes** for fast queries
- **Generic Admin Setup** (no hardcoded user IDs)

## 🚀 Quick Setup (Role Dropdown Only)

If you just want the role dropdown working:

1. **Run the Role Setup SQL:**
   ```sql
   -- Copy and paste ROLE_DROPDOWN_SETUP.sql in Supabase SQL Editor
   ```

2. **Make Yourself Admin:**
   ```sql
   -- Replace with your email
   SELECT make_user_admin('<EMAIL>');
   ```

3. **Refresh Supabase Dashboard** - Role column should now show dropdown!

## 🏗️ Complete Setup (Full Database)

For the complete news website database:

### Step 1: Run Complete Schema
```sql
-- Copy and paste complete_database_schema.sql in Supabase SQL Editor
-- This creates all tables, indexes, triggers, and functions
```

### Step 2: Setup Admin User
```sql
-- Option A: Make specific user admin
SELECT make_user_admin('<EMAIL>');

-- Option B: Make first registered user admin
SELECT setup_first_admin();

-- Option C: Create admin during registration
SELECT create_admin_user('user-uuid', '<EMAIL>', 'Full Name');
```

### Step 3: Verify Setup
1. Check Supabase Table Editor
2. Verify role dropdown works
3. Test admin access in your app

## 🎭 Role System

### Role Hierarchy
```
👑 admin    - Full system access
📝 editor   - Manage content, users, settings  
✍️ writer   - Create and edit articles
👤 user     - Comment, bookmark, profile
👻 guest    - View public content only
```

### Permissions Matrix
| Feature | Guest | User | Writer | Editor | Admin |
|---------|-------|------|--------|--------|-------|
| View Articles | ✅ | ✅ | ✅ | ✅ | ✅ |
| Comment | ❌ | ✅ | ✅ | ✅ | ✅ |
| Bookmark | ❌ | ✅ | ✅ | ✅ | ✅ |
| Create Articles | ❌ | ❌ | ✅ | ✅ | ✅ |
| Edit Any Article | ❌ | ❌ | ❌ | ✅ | ✅ |
| Manage Users | ❌ | ❌ | ❌ | ❌ | ✅ |
| Site Settings | ❌ | ❌ | ❌ | ❌ | ✅ |

## 📊 Database Tables

### Core Tables
- `user_profiles` - User management with roles
- `articles` - Main content with SEO
- `categories` - Dynamic article categories  
- `comments` - Nested comment system
- `bookmarks` - User article bookmarks

### Advanced Tables
- `advertisements` - Ad management with analytics
- `newsletter_subscribers` - Email list management
- `newsletter_templates` - Email templates
- `newsletter_campaigns` - Email campaigns
- `website_settings` - Site configuration
- `media_files` - File upload management
- `article_views` - Analytics tracking
- `user_sessions` - Session management

## 🔒 Security Features

### Row Level Security (RLS)
- Users can only access their own data
- Role-based access to sensitive operations
- Public data accessible to everyone
- Admin override for management functions

### Data Protection
- SQL injection prevention
- Input validation at database level
- Encrypted sensitive data
- Audit trails for changes

## 🛠️ Utility Functions

### User Management
```sql
-- Make user admin
SELECT make_user_admin('<EMAIL>');

-- Check user role
SELECT get_user_role('user-uuid');

-- Check admin access
SELECT has_admin_access('user-uuid');
```

### Statistics
```sql
-- Get article statistics
SELECT get_article_stats();

-- Get user statistics  
SELECT get_user_stats();
```

## 🎨 Frontend Integration

### Role-Based UI
```typescript
// Check user role in your React components
const { profile } = useAuth();
const isAdmin = profile?.role === 'admin';
const hasAdminAccess = ['admin', 'editor'].includes(profile?.role);

// Conditional rendering
{hasAdminAccess && <AdminDashboard />}
```

### API Integration
```typescript
// Your existing database service will work with new schema
import { supabase } from '@/lib/supabase';

// Role-based queries automatically handled by RLS
const { data: articles } = await supabase
  .from('articles')
  .select('*');
```

## 🔧 Customization

### Adding New Roles
```sql
-- Add new role to enum
ALTER TYPE user_role ADD VALUE 'moderator';

-- Update constraint
ALTER TABLE user_profiles 
DROP CONSTRAINT user_profiles_role_check;

ALTER TABLE user_profiles 
ADD CONSTRAINT user_profiles_role_check 
CHECK (role IN ('guest', 'user', 'writer', 'editor', 'moderator', 'admin'));
```

### Adding New Categories
```sql
-- Categories are now dynamic - add via admin panel or SQL
INSERT INTO categories (name, slug, description, color) 
VALUES ('Technology', 'tech', 'Tech news', '#2563EB');
```

## 🐛 Troubleshooting

### Role Dropdown Not Showing
1. Ensure constraint is created: `\d user_profiles`
2. Check enum exists: `\dT user_role`
3. Refresh Supabase dashboard
4. Clear browser cache

### Permission Denied Errors
1. Check RLS policies are enabled
2. Verify user has correct role
3. Check auth.uid() returns correct user ID

### Performance Issues
1. Verify indexes are created
2. Check query execution plans
3. Monitor slow query logs

## 📞 Support

If you encounter issues:
1. Check the setup completion messages
2. Verify all SQL ran without errors
3. Test with simple queries first
4. Check Supabase logs for detailed errors

## 🎉 Success!

Once setup is complete, you'll have:
- ✅ Role dropdown working in Supabase
- ✅ Complete news website database
- ✅ Admin panel ready to use
- ✅ Secure, scalable architecture
- ✅ Performance optimized
- ✅ Production ready

Your news website database is now ready for prime time! 🚀

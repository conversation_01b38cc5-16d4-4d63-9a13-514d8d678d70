import { useState, useEffect } from 'react';
import { bookmarkService } from '@/lib/database';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';

export function useBookmarks() {
  const { user } = useAuth();
  const [bookmarks, setBookmarks] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const loadBookmarks = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const data = await bookmarkService.getUserBookmarks(user.id);
      setBookmarks(data);
    } catch (error) {
      console.error('Error loading bookmarks:', error);
      toast.error('Failed to load bookmarks');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBookmarks();
  }, [user]);

  return {
    bookmarks,
    loading,
    refetch: loadBookmarks
  };
}

export function useBookmarkStatus(articleId: string) {
  const { user } = useAuth();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [loading, setLoading] = useState(false);

  const checkBookmarkStatus = async () => {
    if (!user || !articleId) return;
    
    try {
      const bookmarked = await bookmarkService.isBookmarked(user.id, articleId);
      setIsBookmarked(bookmarked);
    } catch (error) {
      console.error('Error checking bookmark status:', error);
    }
  };

  useEffect(() => {
    checkBookmarkStatus();
  }, [user, articleId]);

  const toggleBookmark = async () => {
    if (!user) {
      toast.error('Please login to bookmark articles');
      return;
    }

    if (!articleId) return;

    setLoading(true);
    try {
      if (isBookmarked) {
        await bookmarkService.removeBookmark(user.id, articleId);
        setIsBookmarked(false);
        toast.success('Article removed from bookmarks');
      } else {
        await bookmarkService.addBookmark(user.id, articleId);
        setIsBookmarked(true);
        toast.success('Article bookmarked successfully');
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      toast.error('Failed to update bookmark');
    } finally {
      setLoading(false);
    }
  };

  return {
    isBookmarked,
    loading,
    toggleBookmark
  };
}

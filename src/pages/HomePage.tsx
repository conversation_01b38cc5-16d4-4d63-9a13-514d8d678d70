import Layout from "@/components/layout/Layout";
import ArticleCarousel from "@/components/news/ArticleCarousel";
import CategoryTabs from "@/components/news/CategoryTabs";
import EditorsPicks from "@/components/news/EditorsPicks";
import AdvertisementPanel from "@/components/news/AdvertisementPanel";
import TrendingArticles from "@/components/news/TrendingArticles";
import { useFeaturedArticles, useTrendingArticles, useArticles } from "@/hooks/useArticles";
import { formatArticlesForDisplay } from "@/lib/articleUtils";
import { advertisementService } from "@/lib/database";
import { useQuery } from "@tanstack/react-query";

export default function HomePage() {
  // Fetch real data from database
  const { data: featuredData, isLoading: featuredLoading } = useFeaturedArticles(5);
  const { data: trendingData, isLoading: trendingLoading } = useTrendingArticles(5);
  const { data: allArticlesData, isLoading: allArticlesLoading } = useArticles({ limit: 20 });

  // Fetch editor's picks (featured articles with high views)
  const { data: editorsPicksData } = useArticles({
    featured: true,
    limit: 4,
  });

  // Fetch advertisements from database
  const { data: adsData } = useQuery({
    queryKey: ['advertisements', 'sidebar'],
    queryFn: () => advertisementService.getAdvertisements({ active: true, position: 'sidebar' }),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Format articles for display
  const featuredArticles = featuredData?.articles?.length
    ? formatArticlesForDisplay(featuredData.articles)
    : [];

  const trendingArticles = trendingData?.articles?.length
    ? formatArticlesForDisplay(trendingData.articles)
    : [];

  const allArticles = allArticlesData?.articles?.length
    ? formatArticlesForDisplay(allArticlesData.articles)
    : [];

  const editorsPicksArticles = editorsPicksData?.articles?.length
    ? formatArticlesForDisplay(editorsPicksData.articles)
    : [];

  // Use real advertisements
  const activeAdvertisements = adsData || [];
  
  // Show loading state while data is being fetched
  if (featuredLoading || trendingLoading || allArticlesLoading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            <div className="lg:col-span-12 text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#D32F2F] mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading latest news...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Left Sidebar - Editor's Picks */}
          <div className="lg:col-span-3 order-3 lg:order-1">
            <EditorsPicks articles={editorsPicksArticles} />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-6 space-y-8 order-1 lg:order-2">
            {/* Top Carousel */}
            {featuredArticles.length > 0 ? (
              <ArticleCarousel articles={featuredArticles} />
            ) : (
              <div className="bg-gray-100 rounded-lg p-8 text-center">
                <p className="text-gray-600">No featured articles available</p>
              </div>
            )}

            {/* Category Tabs */}
            <div>
              <h2 className="text-2xl font-bold mb-4">News Categories</h2>
              {allArticles.length > 0 ? (
                <CategoryTabs articles={allArticles} />
              ) : (
                <div className="bg-gray-100 rounded-lg p-8 text-center">
                  <p className="text-gray-600">No articles available</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Ads & Trending */}
          <div className="lg:col-span-3 space-y-6 order-2 lg:order-3">
            <AdvertisementPanel advertisements={activeAdvertisements} />
            <TrendingArticles articles={trendingArticles} />
          </div>
        </div>
      </div>
    </Layout>
  );
}
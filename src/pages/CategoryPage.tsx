import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import Layout from "@/components/layout/Layout";
import { editorsPicksArticles, advertisements } from "@/data/mockData";
import { Article, Category } from "@/types";
import ArticleCard from "@/components/news/ArticleCard";
import EditorsPicks from "@/components/news/EditorsPicks";
import AdvertisementPanel from "@/components/news/AdvertisementPanel";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { articleService } from "@/lib/database";

export default function CategoryPage() {
  const { categoryName } = useParams<{ categoryName: string }>();
  const [articles, setArticles] = useState<Article[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCategoryArticles();
  }, [categoryName]);

  const loadCategoryArticles = async () => {
    if (!categoryName) return;

    setLoading(true);
    try {
      // Format category name to match database format
      const formattedCategoryName = categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

      const result = await articleService.getArticles({
        category: formattedCategoryName as Category,
        limit: 20
      });

      setArticles(result.articles);
    } catch (error) {
      console.error('Error loading category articles:', error);
      setArticles([]);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!categoryName) return;

    setLoading(true);
    try {
      const formattedCategoryName = categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

      const result = await articleService.getArticles({
        category: formattedCategoryName as Category,
        search: searchQuery,
        limit: 20
      });

      setArticles(result.articles);
    } catch (error) {
      console.error('Error searching articles:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const categoryTitle = categoryName ? categoryName.charAt(0).toUpperCase() + categoryName.slice(1) : "";
  
  return (
    <Layout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">
            <span className="text-[#D32F2F]">{categoryTitle}</span> News
          </h1>
          <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
            <Input
              type="search"
              placeholder={`Search in ${categoryTitle}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button type="submit" className="bg-[#D32F2F] hover:bg-red-700">
              <Search size={18} />
            </Button>
          </form>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-8">
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                    <div className="bg-gray-200 h-6 rounded mb-2"></div>
                    <div className="bg-gray-200 h-4 rounded w-3/4"></div>
                  </div>
                ))}
              </div>
            ) : articles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {articles.map((article) => (
                  <ArticleCard key={article.id} article={article} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">No articles found in this category.</p>
                <p className="text-sm text-gray-400 mt-2">Try searching for something else or check back later.</p>
              </div>
            )}
          </div>
          
          {/* Sidebar */}
          <div className="lg:col-span-4 space-y-6">
            <AdvertisementPanel advertisements={advertisements} />
            <EditorsPicks articles={editorsPicksArticles} />
          </div>
        </div>
      </div>
    </Layout>
  );
}
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { mockArticles } from "@/data/mockData";
import { Category, Advertisement, UserProfile } from "@/types";
import {
  BarChart3,
  PenBox,
  Trash2,
  Users,
  Settings,
  Bell,
  Lock,
  Image,
  Upload,
  FileText,
  UserPlus,
  Search,
  Filter,
  Eye,
  Plus,
  ExternalLink,
  T<PERSON>dingU<PERSON>,
  <PERSON><PERSON>oint<PERSON>
} from "lucide-react";
import { <PERSON> } from "react-router-dom";
import {
  articleService,
  advertisementService,
  userService,
  settingsService,
  newsletterService
} from "@/lib/database";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";
import CreateUserDialog from "@/components/admin/CreateUserDialog";
import CategoryManagement from "@/components/admin/CategoryManagement";

export default function AdminDashboard() {
  const { user, profile, signOut, loading: authLoading } = useAuth();

  // Check if user has admin/editor access
  const hasAdminAccess = profile?.role === 'admin' || profile?.role === 'editor';

  // Show loading while checking authentication, but with timeout
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#D32F2F] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
          <p className="mt-2 text-sm text-gray-500">If this takes too long, please refresh the page</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-center text-gray-900 mb-6">Admin Access Required</h2>
          <p className="text-gray-600 text-center mb-4">
            Please sign in with an admin account to access the dashboard.
          </p>
          <Link
            to="/"
            className="w-full bg-[#D32F2F] text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors text-center block"
          >
            Go to Homepage
          </Link>
        </div>
      </div>
    );
  }

  // If user exists but no profile, show profile loading message
  if (user && !profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-center text-gray-900 mb-6">Loading Profile</h2>
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#D32F2F] mx-auto mb-4"></div>
            <p className="text-gray-600 mb-4">Loading your profile...</p>
            <p className="text-sm text-gray-500">
              If this takes too long, try refreshing the page or contact support.
            </p>
          </div>
          <Link
            to="/"
            className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors text-center block mt-4"
          >
            Go to Homepage
          </Link>
        </div>
      </div>
    );
  }

  // Check if user has admin access
  if (!hasAdminAccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold text-center text-gray-900 mb-6">Access Denied</h2>
          <p className="text-gray-600 text-center mb-4">
            You don't have permission to access the admin dashboard. Only admins and editors can access this area.
          </p>
          <p className="text-sm text-gray-500 text-center mb-4">
            Current role: <span className="font-medium">{profile?.role || 'Unknown'}</span>
          </p>
          <Link
            to="/"
            className="w-full bg-[#D32F2F] text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors text-center block"
          >
            Go to Homepage
          </Link>
        </div>
      </div>
    );
  }

  // Article form state
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [summary, setSummary] = useState("");
  const [category, setCategory] = useState<Category>("Politics");
  const [isBreaking, setIsBreaking] = useState(false);
  const [isFeatured, setIsFeatured] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [searchTerm, setSearchTerm] = useState("");

  // Advertisement form state
  const [adTitle, setAdTitle] = useState("");
  const [adDescription, setAdDescription] = useState("");
  const [adImageUrl, setAdImageUrl] = useState("");
  const [adLinkUrl, setAdLinkUrl] = useState("");
  const [adPosition, setAdPosition] = useState<"header" | "sidebar" | "footer" | "inline" | "popup">("sidebar");
  const [adStartDate, setAdStartDate] = useState("");
  const [adEndDate, setAdEndDate] = useState("");

  // Data state
  const [articles, setArticles] = useState<any[]>([]);
  const [advertisements, setAdvertisements] = useState<Advertisement[]>([]);
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [settings, setSettings] = useState<any>({});
  const [loading, setLoading] = useState(false);

  // Settings form state
  const [siteTitle, setSiteTitle] = useState("");
  const [siteTagline, setSiteTagline] = useState("");
  const [siteDescription, setSiteDescription] = useState("");
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [articlesPerPage, setArticlesPerPage] = useState("10");
  const [commentsEnabled, setCommentsEnabled] = useState(true);
  const [moderateComments, setModerateComments] = useState(true);

  // Newsletter state
  const [subscribers, setSubscribers] = useState<any[]>([]);
  const [subscriberStats, setSubscriberStats] = useState<any>({});
  
  // Load data on component mount
  useEffect(() => {
    loadArticles();
    loadAdvertisements();
    loadUsers();
    loadSettings();
    loadNewsletterData();
  }, []);

  const loadArticles = async () => {
    try {
      const result = await articleService.getArticles({ limit: 20 });
      setArticles(result.articles);
    } catch (error) {
      console.error('Error loading articles:', error);
      toast.error('Failed to load articles');
    }
  };

  const loadAdvertisements = async () => {
    try {
      const ads = await advertisementService.getAdvertisements();
      setAdvertisements(ads);
    } catch (error) {
      console.error('Error loading advertisements:', error);
      toast.error('Failed to load advertisements');
    }
  };

  const loadUsers = async () => {
    try {
      const result = await userService.getUsers({ limit: 50 });
      setUsers(result.users);
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    }
  };

  const loadSettings = async () => {
    try {
      const settingsData = await settingsService.getSettings();
      const settingsMap = settingsData.reduce((acc: any, setting: any) => {
        acc[setting.setting_key] = setting.setting_value;
        return acc;
      }, {});

      setSettings(settingsMap);

      // Update form state with loaded settings
      setSiteTitle(settingsMap.site_title || "");
      setSiteTagline(settingsMap.site_tagline || "");
      setSiteDescription(settingsMap.site_description || "");
      setMaintenanceMode(settingsMap.maintenance_mode === 'true');
      setArticlesPerPage(settingsMap.articles_per_page || "10");
      setCommentsEnabled(settingsMap.comments_enabled !== 'false');
      setModerateComments(settingsMap.moderate_comments !== 'false');
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load settings');
    }
  };

  const loadNewsletterData = async () => {
    try {
      const [subscribersData, statsData] = await Promise.all([
        newsletterService.getSubscribers(),
        newsletterService.getSubscriberStats()
      ]);

      setSubscribers(subscribersData);
      setSubscriberStats(statsData);
    } catch (error) {
      console.error('Error loading newsletter data:', error);
      toast.error('Failed to load newsletter data');
    }
  };

  const handleArticleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    try {
      await articleService.createArticle({
        title,
        summary,
        content,
        category,
        image_url: imageUrl,
        author_id: user.id,
        published_at: new Date().toISOString(),
        is_breaking: isBreaking,
        is_featured: isFeatured,
        is_trending: false
      });

      toast.success('Article created successfully!');

      // Reset form
      setTitle("");
      setSummary("");
      setContent("");
      setImageUrl("");
      setCategory("Politics");
      setIsBreaking(false);
      setIsFeatured(false);

      // Reload articles
      loadArticles();
    } catch (error) {
      console.error('Error creating article:', error);
      toast.error('Failed to create article');
    } finally {
      setLoading(false);
    }
  };

  const handleAdSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    try {
      await advertisementService.createAdvertisement({
        title: adTitle,
        description: adDescription,
        image_url: adImageUrl,
        link_url: adLinkUrl,
        position: adPosition,
        is_active: true,
        start_date: adStartDate || new Date().toISOString(),
        end_date: adEndDate || null,
        created_by: user.id
      });

      toast.success('Advertisement created successfully!');

      // Reset form
      setAdTitle("");
      setAdDescription("");
      setAdImageUrl("");
      setAdLinkUrl("");
      setAdPosition("sidebar");
      setAdStartDate("");
      setAdEndDate("");

      // Reload advertisements
      loadAdvertisements();
    } catch (error) {
      console.error('Error creating advertisement:', error);
      toast.error('Failed to create advertisement');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteArticle = async (id: string) => {
    if (!confirm('Are you sure you want to delete this article?')) return;

    try {
      await articleService.deleteArticle(id);
      toast.success('Article deleted successfully!');
      loadArticles();
    } catch (error) {
      console.error('Error deleting article:', error);
      toast.error('Failed to delete article');
    }
  };

  const handleDeleteAd = async (id: string) => {
    if (!confirm('Are you sure you want to delete this advertisement?')) return;

    try {
      await advertisementService.deleteAdvertisement(id);
      toast.success('Advertisement deleted successfully!');
      loadAdvertisements();
    } catch (error) {
      console.error('Error deleting advertisement:', error);
      toast.error('Failed to delete advertisement');
    }
  };

  const toggleAdStatus = async (id: string, currentStatus: boolean) => {
    try {
      await advertisementService.updateAdvertisement(id, { is_active: !currentStatus });
      toast.success(`Advertisement ${!currentStatus ? 'activated' : 'deactivated'} successfully!`);
      loadAdvertisements();
    } catch (error) {
      console.error('Error updating advertisement:', error);
      toast.error('Failed to update advertisement');
    }
  };

  const handleSaveGeneralSettings = async () => {
    if (!user) return;

    setLoading(true);
    try {
      await settingsService.updateSettings([
        { key: 'site_title', value: siteTitle },
        { key: 'site_tagline', value: siteTagline },
        { key: 'site_description', value: siteDescription },
        { key: 'maintenance_mode', value: maintenanceMode.toString() }
      ], user.id);

      toast.success('General settings saved successfully!');
      loadSettings();
    } catch (error) {
      console.error('Error saving general settings:', error);
      toast.error('Failed to save general settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveContentSettings = async () => {
    if (!user) return;

    setLoading(true);
    try {
      await settingsService.updateSettings([
        { key: 'articles_per_page', value: articlesPerPage },
        { key: 'comments_enabled', value: commentsEnabled.toString() },
        { key: 'moderate_comments', value: moderateComments.toString() }
      ], user.id);

      toast.success('Content settings saved successfully!');
      loadSettings();
    } catch (error) {
      console.error('Error saving content settings:', error);
      toast.error('Failed to save content settings');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (id: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      await userService.deleteUser(id);
      toast.success('User deleted successfully!');
      loadUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    }
  };

  const handleUpdateUserRole = async (id: string, newRole: string) => {
    try {
      await userService.updateUser(id, { role: newRole as any });
      toast.success('User role updated successfully!');
      loadUsers();
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('Failed to update user role');
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-[#121212] text-white py-4 px-6 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Link to="/" className="text-xl font-bold text-[#D32F2F]">THE SACH PATRA</Link>
          <span className="text-sm">Admin Dashboard</span>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm">Welcome, {profile?.full_name}</span>
          <Bell className="h-5 w-5 cursor-pointer hover:text-[#D32F2F]" />
          <Button variant="outline" onClick={signOut}>Logout</Button>
        </div>
      </header>
      
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="create" className="w-full">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="create">Create Article</TabsTrigger>
            <TabsTrigger value="manage">Manage Articles</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="advertisements">Advertisements</TabsTrigger>
            <TabsTrigger value="users">User Management</TabsTrigger>
            <TabsTrigger value="settings">Website Settings</TabsTrigger>
            <TabsTrigger value="newsletter">Newsletter</TabsTrigger>
          </TabsList>
          
          {/* Create Article Tab */}
          <TabsContent value="create" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Create New Article</CardTitle>
                <CardDescription>
                  Fill in the details to create a new article.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleArticleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium mb-1">
                      Article Title
                    </label>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Enter article title"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="summary" className="block text-sm font-medium mb-1">
                      Article Summary
                    </label>
                    <Textarea
                      id="summary"
                      value={summary}
                      onChange={(e) => setSummary(e.target.value)}
                      placeholder="Enter a brief summary of the article"
                      rows={3}
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="category" className="block text-sm font-medium mb-1">
                      Category
                    </label>
                    <Select
                      value={category}
                      onValueChange={(value) => setCategory(value as Category)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Politics">Politics</SelectItem>
                        <SelectItem value="Tech">Tech</SelectItem>
                        <SelectItem value="World">World</SelectItem>
                        <SelectItem value="Sports">Sports</SelectItem>
                        <SelectItem value="Entertainment">Entertainment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label htmlFor="content" className="block text-sm font-medium mb-1">
                      Article Content
                    </label>
                    <Textarea
                      id="content"
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="Enter article content"
                      rows={10}
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="imageUrl" className="block text-sm font-medium mb-1">
                      Featured Image URL
                    </label>
                    <Input
                      id="imageUrl"
                      value={imageUrl}
                      onChange={(e) => setImageUrl(e.target.value)}
                      placeholder="Enter image URL"
                      type="url"
                      required
                    />
                  </div>

                  <div className="flex space-x-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={isBreaking}
                        onChange={(e) => setIsBreaking(e.target.checked)}
                        className="rounded border-gray-300 text-[#D32F2F] focus:ring-[#D32F2F]"
                      />
                      <span>Breaking News</span>
                    </label>

                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={isFeatured}
                        onChange={(e) => setIsFeatured(e.target.checked)}
                        className="rounded border-gray-300 text-[#D32F2F] focus:ring-[#D32F2F]"
                      />
                      <span>Featured Article</span>
                    </label>
                  </div>

                  <Button
                    type="submit"
                    className="bg-[#D32F2F] hover:bg-red-700"
                    disabled={loading}
                  >
                    {loading ? 'Creating...' : 'Create Article'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Manage Articles Tab */}
          <TabsContent value="manage">
            <Card>
              <CardHeader>
                <CardTitle>Manage Articles</CardTitle>
                <CardDescription>
                  View, edit, or delete existing articles.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      className="w-[300px] pl-9"
                      type="search"
                      placeholder="Search articles..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Author</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Views</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {articles
                      .filter(article =>
                        !searchTerm ||
                        article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        article.category.toLowerCase().includes(searchTerm.toLowerCase())
                      )
                      .map((article) => (
                      <TableRow key={article.id}>
                        <TableCell className="font-medium">
                          <div className="max-w-[200px] truncate" title={article.title}>
                            {article.title}
                          </div>
                        </TableCell>
                        <TableCell>{article.category}</TableCell>
                        <TableCell>{article.author?.full_name || 'Unknown'}</TableCell>
                        <TableCell>{new Date(article.published_at).toLocaleDateString()}</TableCell>
                        <TableCell>{article.views}</TableCell>
                        <TableCell>
                          {article.is_breaking ?
                            <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Breaking</span> :
                            article.is_featured ?
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Featured</span> :
                              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Published</span>
                          }
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline" title="View Article">
                              <Eye size={16} />
                            </Button>
                            <Button size="sm" variant="outline" title="Edit Article">
                              <PenBox size={16} />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-[#D32F2F]"
                              title="Delete Article"
                              onClick={() => handleDeleteArticle(article.id)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {articles.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No articles found. Create your first article!
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Advertisement Management Tab */}
          <TabsContent value="advertisements">
            <div className="space-y-6">
              {/* Create Advertisement Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Plus className="mr-2 h-5 w-5" />
                    Create New Advertisement
                  </CardTitle>
                  <CardDescription>
                    Create and manage advertisements for your website.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleAdSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="adTitle" className="block text-sm font-medium mb-1">
                          Advertisement Title
                        </label>
                        <Input
                          id="adTitle"
                          value={adTitle}
                          onChange={(e) => setAdTitle(e.target.value)}
                          placeholder="Enter advertisement title"
                          required
                        />
                      </div>

                      <div>
                        <label htmlFor="adPosition" className="block text-sm font-medium mb-1">
                          Position
                        </label>
                        <Select
                          value={adPosition}
                          onValueChange={(value) => setAdPosition(value as any)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select position" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="header">Header</SelectItem>
                            <SelectItem value="sidebar">Sidebar</SelectItem>
                            <SelectItem value="footer">Footer</SelectItem>
                            <SelectItem value="inline">Inline</SelectItem>
                            <SelectItem value="popup">Popup</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <label htmlFor="adDescription" className="block text-sm font-medium mb-1">
                        Description
                      </label>
                      <Textarea
                        id="adDescription"
                        value={adDescription}
                        onChange={(e) => setAdDescription(e.target.value)}
                        placeholder="Enter advertisement description"
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="adImageUrl" className="block text-sm font-medium mb-1">
                          Image URL
                        </label>
                        <Input
                          id="adImageUrl"
                          value={adImageUrl}
                          onChange={(e) => setAdImageUrl(e.target.value)}
                          placeholder="Enter image URL"
                          type="url"
                          required
                        />
                      </div>

                      <div>
                        <label htmlFor="adLinkUrl" className="block text-sm font-medium mb-1">
                          Link URL
                        </label>
                        <Input
                          id="adLinkUrl"
                          value={adLinkUrl}
                          onChange={(e) => setAdLinkUrl(e.target.value)}
                          placeholder="Enter link URL"
                          type="url"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="adStartDate" className="block text-sm font-medium mb-1">
                          Start Date (Optional)
                        </label>
                        <Input
                          id="adStartDate"
                          value={adStartDate}
                          onChange={(e) => setAdStartDate(e.target.value)}
                          type="datetime-local"
                        />
                      </div>

                      <div>
                        <label htmlFor="adEndDate" className="block text-sm font-medium mb-1">
                          End Date (Optional)
                        </label>
                        <Input
                          id="adEndDate"
                          value={adEndDate}
                          onChange={(e) => setAdEndDate(e.target.value)}
                          type="datetime-local"
                        />
                      </div>
                    </div>

                    <Button
                      type="submit"
                      className="bg-[#D32F2F] hover:bg-red-700"
                      disabled={loading}
                    >
                      {loading ? 'Creating...' : 'Create Advertisement'}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              {/* Advertisement Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="flex flex-col items-center p-6">
                    <BarChart3 size={32} className="text-[#1976D2] mb-2" />
                    <div className="text-2xl font-bold">{advertisements.length}</div>
                    <p className="text-sm text-gray-500">Total Ads</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="flex flex-col items-center p-6">
                    <TrendingUp size={32} className="text-green-600 mb-2" />
                    <div className="text-2xl font-bold">{advertisements.filter(ad => ad.is_active).length}</div>
                    <p className="text-sm text-gray-500">Active Ads</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="flex flex-col items-center p-6">
                    <MousePointer size={32} className="text-[#D32F2F] mb-2" />
                    <div className="text-2xl font-bold">{advertisements.reduce((sum, ad) => sum + ad.clicks, 0)}</div>
                    <p className="text-sm text-gray-500">Total Clicks</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="flex flex-col items-center p-6">
                    <Eye size={32} className="text-purple-600 mb-2" />
                    <div className="text-2xl font-bold">{advertisements.reduce((sum, ad) => sum + ad.impressions, 0)}</div>
                    <p className="text-sm text-gray-500">Total Impressions</p>
                  </CardContent>
                </Card>
              </div>

              {/* Advertisement List */}
              <Card>
                <CardHeader>
                  <CardTitle>Manage Advertisements</CardTitle>
                  <CardDescription>
                    View and manage all your advertisements.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Position</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Clicks</TableHead>
                        <TableHead>Impressions</TableHead>
                        <TableHead>CTR</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {advertisements.map((ad) => (
                        <TableRow key={ad.id}>
                          <TableCell className="font-medium">
                            <div className="max-w-[200px] truncate" title={ad.title}>
                              {ad.title}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs capitalize">
                              {ad.position}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              ad.is_active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {ad.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </TableCell>
                          <TableCell>{ad.clicks}</TableCell>
                          <TableCell>{ad.impressions}</TableCell>
                          <TableCell>
                            {ad.impressions > 0 ? ((ad.clicks / ad.impressions) * 100).toFixed(2) + '%' : '0%'}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => toggleAdStatus(ad.id, ad.is_active)}
                                title={ad.is_active ? 'Deactivate' : 'Activate'}
                              >
                                {ad.is_active ? 'Deactivate' : 'Activate'}
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-[#D32F2F]"
                                onClick={() => handleDeleteAd(ad.id)}
                                title="Delete Advertisement"
                              >
                                <Trash2 size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {advertisements.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No advertisements found. Create your first advertisement!
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Category Management Tab */}
          <TabsContent value="categories">
            <div className="space-y-4">
              <CategoryManagement />
            </div>
          </TabsContent>

          {/* User Management Tab */}
          <TabsContent value="users">
            <div className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-2xl font-bold">User Management</CardTitle>
                  <CreateUserDialog onUserCreated={loadUsers} />
                </CardHeader>
                <CardContent>
                  <div className="mb-4 flex justify-between">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                      <Input
                        className="w-[300px] pl-9"
                        type="search"
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Filter className="h-4 w-4 text-gray-500" />
                      <Select defaultValue="all">
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Roles</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="editor">Editor</SelectItem>
                          <SelectItem value="writer">Writer</SelectItem>
                          <SelectItem value="subscriber">Subscriber</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Joined</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users
                        .filter(user =>
                          !searchTerm ||
                          user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          user.full_name.toLowerCase().includes(searchTerm.toLowerCase())
                        )
                        .map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">{user.full_name}</TableCell>
                          <TableCell>{user.username}</TableCell>
                          <TableCell>
                            <Select
                              value={user.role}
                              onValueChange={(value) => handleUpdateUserRole(user.id, value)}
                            >
                              <SelectTrigger className="w-[120px]">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="user">User</SelectItem>
                                <SelectItem value="writer">Writer</SelectItem>
                                <SelectItem value="editor">Editor</SelectItem>
                                <SelectItem value="admin">Admin</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                              Active
                            </span>
                          </TableCell>
                          <TableCell>{new Date(user.created_at).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline" title="Edit User">
                                <PenBox size={16} />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-[#D32F2F]"
                                title="Delete User"
                                onClick={() => handleDeleteUser(user.id)}
                              >
                                <Trash2 size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {users.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No users found.
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Permission Settings</CardTitle>
                  <CardDescription>Define what users can do based on their roles</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Permission</TableHead>
                        <TableHead>Admin</TableHead>
                        <TableHead>Editor</TableHead>
                        <TableHead>Writer</TableHead>
                        <TableHead>Subscriber</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>Create Articles</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Edit Any Article</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Delete Articles</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Manage Users</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Change Site Settings</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          {/* Website Settings Tab */}
          <TabsContent value="settings">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings className="mr-2 h-5 w-5" /> General Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="site-title" className="block text-sm font-medium mb-1">Site Title</label>
                    <Input
                      id="site-title"
                      value={siteTitle}
                      onChange={(e) => setSiteTitle(e.target.value)}
                      placeholder="Enter site title"
                    />
                  </div>

                  <div>
                    <label htmlFor="site-tagline" className="block text-sm font-medium mb-1">Tagline</label>
                    <Input
                      id="site-tagline"
                      value={siteTagline}
                      onChange={(e) => setSiteTagline(e.target.value)}
                      placeholder="Enter site tagline"
                    />
                  </div>

                  <div>
                    <label htmlFor="site-description" className="block text-sm font-medium mb-1">Site Description</label>
                    <Textarea
                      id="site-description"
                      value={siteDescription}
                      onChange={(e) => setSiteDescription(e.target.value)}
                      placeholder="Enter site description"
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="maintenance-mode"
                      checked={maintenanceMode}
                      onCheckedChange={setMaintenanceMode}
                    />
                    <label htmlFor="maintenance-mode">Maintenance Mode</label>
                  </div>

                  <Button
                    className="bg-[#D32F2F] hover:bg-red-700 mt-4"
                    onClick={handleSaveGeneralSettings}
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : 'Save Settings'}
                  </Button>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Image className="mr-2 h-5 w-5" /> Appearance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="logo" className="block text-sm font-medium mb-1">Site Logo</label>
                    <div className="flex items-center space-x-4">
                      <div className="h-16 w-16 bg-gray-200 rounded flex items-center justify-center">
                        <span className="text-gray-500">Logo</span>
                      </div>
                      <div>
                        <Input id="logo" type="file" />
                        <p className="text-xs text-gray-500 mt-1">Recommended size: 200x50px</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="favicon" className="block text-sm font-medium mb-1">Favicon</label>
                    <div className="flex items-center space-x-4">
                      <div className="h-8 w-8 bg-gray-200 rounded flex items-center justify-center">
                        <span className="text-gray-500 text-xs">Icon</span>
                      </div>
                      <div>
                        <Input id="favicon" type="file" />
                        <p className="text-xs text-gray-500 mt-1">Recommended size: 32x32px</p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Color Scheme</label>
                    <div className="grid grid-cols-4 gap-2">
                      <div className="flex flex-col items-center">
                        <div className="h-8 w-8 bg-[#D32F2F] rounded-full mb-1"></div>
                        <span className="text-xs">Primary</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="h-8 w-8 bg-[#1976D2] rounded-full mb-1"></div>
                        <span className="text-xs">Secondary</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="h-8 w-8 bg-[#121212] rounded-full mb-1"></div>
                        <span className="text-xs">Text</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="h-8 w-8 bg-white border border-gray-200 rounded-full mb-1"></div>
                        <span className="text-xs">Background</span>
                      </div>
                    </div>
                  </div>

                  <Button className="bg-[#D32F2F] hover:bg-red-700 mt-4">Save Appearance</Button>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" /> Content Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="posts-per-page" className="block text-sm font-medium mb-1">Articles Per Page</label>
                    <Select
                      value={articlesPerPage}
                      onValueChange={setArticlesPerPage}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select number" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5</SelectItem>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="15">15</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Featured Categories</label>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-politics" defaultChecked />
                        <label htmlFor="cat-politics">Politics</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-tech" defaultChecked />
                        <label htmlFor="cat-tech">Tech</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-world" defaultChecked />
                        <label htmlFor="cat-world">World</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-sports" defaultChecked />
                        <label htmlFor="cat-sports">Sports</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-entertainment" defaultChecked />
                        <label htmlFor="cat-entertainment">Entertainment</label>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center justify-between">
                      <label htmlFor="comments-enabled" className="text-sm font-medium">Enable Comments</label>
                      <Switch
                        id="comments-enabled"
                        checked={commentsEnabled}
                        onCheckedChange={setCommentsEnabled}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <label htmlFor="moderate-comments" className="text-sm font-medium">Moderate Comments</label>
                      <Switch
                        id="moderate-comments"
                        checked={moderateComments}
                        onCheckedChange={setModerateComments}
                      />
                    </div>
                  </div>

                  <Button
                    className="bg-[#D32F2F] hover:bg-red-700 mt-4"
                    onClick={handleSaveContentSettings}
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : 'Save Content Settings'}
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Lock className="mr-2 h-5 w-5" /> Security Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center justify-between">
                      <label htmlFor="two-factor" className="text-sm font-medium">Two-Factor Authentication</label>
                      <Switch id="two-factor" />
                    </div>
                    <div className="flex items-center justify-between">
                      <label htmlFor="captcha" className="text-sm font-medium">CAPTCHA on Forms</label>
                      <Switch id="captcha" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <label htmlFor="auto-logout" className="text-sm font-medium">Auto Logout (30 min inactive)</label>
                      <Switch id="auto-logout" defaultChecked />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="password-policy" className="block text-sm font-medium mb-1">Password Policy</label>
                    <Select defaultValue="strong">
                      <SelectTrigger>
                        <SelectValue placeholder="Select policy" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="basic">Basic (8+ characters)</SelectItem>
                        <SelectItem value="medium">Medium (8+ chars, numbers + letters)</SelectItem>
                        <SelectItem value="strong">Strong (8+ chars, numbers, letters, special)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button className="bg-[#D32F2F] hover:bg-red-700 mt-4">Save Security Settings</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Newsletter Management Tab - New Addition */}
          <TabsContent value="newsletter">
            <div className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div>
                    <CardTitle className="text-2xl font-bold">Newsletter Management</CardTitle>
                    <CardDescription>Create and manage email newsletters</CardDescription>
                  </div>
                  <Button className="bg-[#D32F2F] hover:bg-red-700">
                    <Upload className="mr-2 h-4 w-4" /> Create Newsletter
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <div>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg">Subscriber Statistics</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="flex flex-col space-y-4">
                              <div className="flex justify-between items-center">
                                <span>Total Subscribers</span>
                                <span className="font-bold">{subscriberStats.total || 0}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span>Active Subscribers</span>
                                <span className="font-bold">{subscriberStats.active || 0}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span>Unsubscribed</span>
                                <span className="font-bold">{subscriberStats.unsubscribed || 0}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span>Retention Rate</span>
                                <span className="font-bold">
                                  {subscriberStats.total > 0
                                    ? ((subscriberStats.active / subscriberStats.total) * 100).toFixed(1) + '%'
                                    : '0%'
                                  }
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span>Growth This Month</span>
                                <span className="font-bold">+{Math.floor(Math.random() * 50)}</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg">Recent Growth</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <div>
                                <div className="flex justify-between mb-1">
                                  <span>This Month</span>
                                  <span>+142</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "65%" }}></div>
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-between mb-1">
                                  <span>Last Month</span>
                                  <span>+98</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "45%" }}></div>
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-between mb-1">
                                  <span>3 Months Ago</span>
                                  <span>+78</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "35%" }}></div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-lg font-medium mb-4">Recent Newsletters</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Title</TableHead>
                          <TableHead>Date Sent</TableHead>
                          <TableHead>Recipients</TableHead>
                          <TableHead>Open Rate</TableHead>
                          <TableHead>Click Rate</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">Weekly Digest: Top Politics Stories</TableCell>
                          <TableCell>July 1, 2023</TableCell>
                          <TableCell>3,845</TableCell>
                          <TableCell>42%</TableCell>
                          <TableCell>15%</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye size={16} />
                              </Button>
                              <Button size="sm" variant="outline">
                                <PenBox size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">Breaking News: Special Edition</TableCell>
                          <TableCell>June 28, 2023</TableCell>
                          <TableCell>3,912</TableCell>
                          <TableCell>58%</TableCell>
                          <TableCell>23%</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye size={16} />
                              </Button>
                              <Button size="sm" variant="outline">
                                <PenBox size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">Tech Innovations Monthly</TableCell>
                          <TableCell>June 15, 2023</TableCell>
                          <TableCell>2,756</TableCell>
                          <TableCell>36%</TableCell>
                          <TableCell>14%</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye size={16} />
                              </Button>
                              <Button size="sm" variant="outline">
                                <PenBox size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                  
                  <div className="mt-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Newsletter Template</CardTitle>
                        <CardDescription>Configure your newsletter template</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <label htmlFor="template-name" className="block text-sm font-medium mb-1">Template Name</label>
                          <Input id="template-name" defaultValue="Standard Newsletter Template" />
                        </div>
                        
                        <div>
                          <label htmlFor="template-subject" className="block text-sm font-medium mb-1">Default Subject Line</label>
                          <Input id="template-subject" defaultValue="[The Sach Patra] Weekly Newsletter" />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium mb-1">Include Sections</label>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" id="section-top-stories" defaultChecked />
                              <label htmlFor="section-top-stories">Top Stories</label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" id="section-breaking" defaultChecked />
                              <label htmlFor="section-breaking">Breaking News</label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" id="section-popular" defaultChecked />
                              <label htmlFor="section-popular">Most Popular</label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" id="section-editor" defaultChecked />
                              <label htmlFor="section-editor">Editor's Picks</label>
                            </div>
                          </div>
                        </div>
                        
                        <Button className="bg-[#D32F2F] hover:bg-red-700">Save Template</Button>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

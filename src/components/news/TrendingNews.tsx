import { Article } from "@/types";
import { Link } from "react-router-dom";
import { Eye, TrendingUp } from "lucide-react";

interface TrendingNewsProps {
  articles: Article[];
}

export default function TrendingNews({ articles }: TrendingNewsProps) {
  const trendingArticles = articles
    .filter(article => article.is_trending || article.views > 400)
    .slice(0, 5);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg border p-6">
      <div className="flex items-center mb-6">
        <TrendingUp className="h-6 w-6 text-red-500 mr-2" />
        <h2 className="text-xl font-bold text-gray-900">Trending News</h2>
      </div>
      
      <div className="space-y-4">
        {trendingArticles.map((article, index) => (
          <div key={article.id} className="group">
            <Link to={`/article/${article.id}`} className="block">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-base line-clamp-2 group-hover:text-primary transition-colors mb-2">
                    {article.title}
                  </h3>
                  <div className="flex items-center text-sm text-gray-500 space-x-3">
                    <span className="px-2 py-1 bg-gray-100 rounded text-xs font-medium">
                      {article.category}
                    </span>
                    <span>{formatDate(article.published_at)}</span>
                    <span className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      {article.views}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}

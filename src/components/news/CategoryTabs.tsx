import { Article, Category } from "@/types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Link } from "react-router-dom";
import { Eye } from "lucide-react";

interface CategoryTabsProps {
  articles: Article[];
}

export default function CategoryTabs({ articles }: CategoryTabsProps) {
  const categories: Category[] = ["Politics", "Tech", "World", "Sports", "Entertainment"];

  const getArticlesByCategory = (category: Category) => {
    return articles.filter((article) => article.category === category).slice(0, 4);
  };

  return (
    <div className="w-full">
      <Tabs defaultValue="Politics" className="w-full">
        <TabsList className="w-full grid grid-cols-5 bg-gray-100 rounded-lg p-1">
          {categories.map((category) => (
            <TabsTrigger
              key={category}
              value={category}
              className="text-sm font-medium data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
            >
              {category}
            </TabsTrigger>
          ))}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category} value={category} className="mt-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {getArticlesByCategory(category).map((article) => (
                <div key={article.id} className="group">
                  <Link to={`/article/${article.id}`} className="block">
                    <div className="relative overflow-hidden rounded-lg mb-3">
                      <img
                        src={article.image_url}
                        alt={article.title}
                        className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute top-3 left-3">
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-primary text-white">
                          {article.category}
                        </span>
                      </div>
                    </div>
                    <h3 className="font-bold text-lg mb-2 group-hover:text-primary transition-colors line-clamp-2">
                      {article.title}
                    </h3>
                    <p className="text-gray-600 text-sm line-clamp-2 mb-3">{article.summary}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>
                        {new Date(article.published_at).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </span>
                      <span className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        {article.views}
                      </span>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
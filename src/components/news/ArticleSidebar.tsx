import { Article } from "@/types";
import { Link } from "react-router-dom";
import { Eye, Clock } from "lucide-react";

interface ArticleSidebarProps {
  currentArticle: Article;
  relatedArticles: Article[];
  onArticleClick: (articleId: string) => void;
}

export default function ArticleSidebar({ 
  currentArticle, 
  relatedArticles, 
  onArticleClick 
}: ArticleSidebarProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="w-full lg:w-80 space-y-6">
      {/* Current Article Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-bold text-lg mb-2">Current Article</h3>
        <div className="flex items-center text-sm text-gray-600 mb-2">
          <span className="px-2 py-1 bg-primary text-white rounded-full text-xs mr-2">
            {currentArticle.category}
          </span>
          <Clock className="h-4 w-4 mr-1" />
          {formatDate(currentArticle.published_at)}
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <Eye className="h-4 w-4 mr-1" />
          {currentArticle.views} views
        </div>
      </div>

      {/* Related Articles */}
      <div className="bg-white rounded-lg border p-4">
        <h3 className="font-bold text-lg mb-4 text-gray-900">Related Articles</h3>
        <div className="space-y-4">
          {relatedArticles.map((article) => (
            <div key={article.id} className="group cursor-pointer">
              <Link 
                to={`/article/${article.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  onArticleClick(article.id);
                  scrollToTop();
                }}
                className="block"
              >
                <div className="flex space-x-3">
                  <div className="flex-shrink-0">
                    <img 
                      src={article.image_url} 
                      alt={article.title}
                      className="w-16 h-16 object-cover rounded-lg group-hover:opacity-80 transition-opacity"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-sm line-clamp-2 group-hover:text-primary transition-colors mb-1">
                      {article.title}
                    </h4>
                    <div className="flex items-center text-xs text-gray-500 space-x-2">
                      <span className="px-1.5 py-0.5 bg-gray-100 rounded text-xs">
                        {article.category}
                      </span>
                      <span>{formatDate(article.published_at)}</span>
                    </div>
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                      <Eye className="h-3 w-3 mr-1" />
                      {article.views}
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* Trending Articles */}
      <div className="bg-white rounded-lg border p-4">
        <h3 className="font-bold text-lg mb-4 text-gray-900">Trending Now</h3>
        <div className="space-y-3">
          {relatedArticles
            .filter(article => article.is_trending)
            .slice(0, 3)
            .map((article, index) => (
            <div key={article.id} className="group cursor-pointer">
              <Link 
                to={`/article/${article.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  onArticleClick(article.id);
                  scrollToTop();
                }}
                className="block"
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm line-clamp-2 group-hover:text-primary transition-colors">
                      {article.title}
                    </h4>
                    <div className="flex items-center text-xs text-gray-500 mt-1 space-x-2">
                      <span>{article.category}</span>
                      <span>•</span>
                      <span className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        {article.views}
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* Advertisement Space */}
      <div className="bg-gray-100 rounded-lg p-4 text-center">
        <div className="text-xs text-gray-500 mb-2">Advertisement</div>
        <div className="bg-white rounded border-2 border-dashed border-gray-300 p-8">
          <div className="text-gray-400 text-sm">Ad Space</div>
          <div className="text-gray-400 text-xs">300x250</div>
        </div>
      </div>
    </div>
  );
}

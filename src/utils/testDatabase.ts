import { supabase } from '@/lib/supabase';

export const testDatabaseConnection = async () => {
  try {
    console.log('Testing database connection...');
    
    // Test basic connection
    const { data: testData, error: testError } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('Database connection test failed:', testError);
      return false;
    }
    
    console.log('Database connection successful');
    return true;
  } catch (error) {
    console.error('Database connection error:', error);
    return false;
  }
};

export const testUserProfile = async (userId: string) => {
  try {
    console.log('Testing user profile fetch for:', userId);
    
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('Profile fetch error:', error);
      return null;
    }
    
    console.log('Profile found:', data);
    return data;
  } catch (error) {
    console.error('Profile test error:', error);
    return null;
  }
};

export const testAuthState = async () => {
  try {
    console.log('Testing auth state...');
    
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Auth session error:', error);
      return null;
    }
    
    console.log('Auth session:', session ? 'exists' : 'null');
    console.log('User ID:', session?.user?.id || 'null');
    
    return session;
  } catch (error) {
    console.error('Auth test error:', error);
    return null;
  }
};

export const createTestProfile = async (userId: string, email: string) => {
  try {
    console.log('Creating test profile for:', userId);
    
    const username = email.split('@')[0];
    const { data, error } = await supabase
      .from('user_profiles')
      .insert({
        id: userId,
        username: username,
        full_name: username,
        avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`,
        role: 'admin' // Make test user admin
      })
      .select()
      .single();
    
    if (error) {
      console.error('Profile creation error:', error);
      return null;
    }
    
    console.log('Profile created:', data);
    return data;
  } catch (error) {
    console.error('Profile creation test error:', error);
    return null;
  }
};

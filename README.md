# The Sach Patra - News Website with Admin Panel

A modern, full-featured news website built with React, TypeScript, Vite, and Supabase. Features a comprehensive admin panel for content management, user management, advertisement management, and website settings.

## 🚀 Features

### Frontend Features
- **Modern Design**: Clean, responsive design with dark/light theme support
- **Article Management**: Browse articles by category, search functionality
- **User Authentication**: Sign up, sign in, profile management
- **Comments System**: Interactive commenting on articles
- **Newsletter Subscription**: Email newsletter signup
- **Bookmarking**: Save articles for later reading
- **Mobile Responsive**: Optimized for all device sizes

### Admin Panel Features
- **Article Management**: Create, edit, delete articles with rich text editor
- **Advertisement Management**: Create and manage ads with performance tracking
- **User Management**: Manage user roles and permissions
- **Website Settings**: Configure site settings, appearance, and content policies
- **Newsletter Management**: Manage subscribers and email campaigns
- **Analytics Dashboard**: Track article views, user engagement, and ad performance
- **Role-Based Access Control**: Admin, Editor, Writer, and User roles

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **UI Components**: Tailwind CSS, Radix UI, Lucide Icons
- **Backend**: Supabase (PostgreSQL, Authentication, Real-time)
- **State Management**: React Context API
- **Form Handling**: React Hook Form
- **Notifications**: Sonner
- **Routing**: React Router DOM

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd news-main
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Create a `.env.local` file in the root directory:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Set Up Database

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase-schema.sql`
4. Run the SQL to create all tables, functions, and policies

### 5. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## 🗄️ Database Schema

The application uses the following main tables:

- **user_profiles**: User information and roles
- **articles**: News articles with metadata
- **comments**: Article comments
- **bookmarks**: User bookmarks
- **advertisements**: Advertisement management
- **website_settings**: Site configuration
- **newsletter_subscribers**: Email subscribers
- **newsletter_templates**: Email templates
- **newsletter_campaigns**: Email campaigns

## 👥 User Roles

- **Admin**: Full access to all features
- **Editor**: Can manage articles, advertisements, and newsletters
- **Writer**: Can create and edit own articles
- **User**: Can read articles, comment, and bookmark

## 🔧 Admin Panel Access

1. Create a user account through the normal signup process
2. In your Supabase dashboard, go to Table Editor > user_profiles
3. Find your user and change the `role` field to `admin` or `editor`
4. Access the admin panel at `/admin`

## 📊 SQL Queries for Analytics

The `admin-sql-queries.sql` file contains comprehensive SQL queries for:

- Article analytics and performance metrics
- User engagement statistics
- Advertisement performance tracking
- Newsletter campaign analytics
- Content management insights
- Website performance optimization

## 🎨 Customization

### Styling
- Colors can be customized in `tailwind.config.ts`
- Main brand colors: Primary (#D32F2F), Secondary (#1976D2)

### Content
- Site title, tagline, and description can be changed in the admin panel
- Logo and favicon can be uploaded through the admin interface

## 📱 API Endpoints

The application uses Supabase's auto-generated REST API. Key services include:

- `articleService`: Article CRUD operations
- `userService`: User management
- `advertisementService`: Advertisement management
- `settingsService`: Website settings
- `newsletterService`: Newsletter management

## 🔒 Security Features

- Row Level Security (RLS) enabled on all tables
- Role-based access control
- Secure authentication with Supabase Auth
- Input validation and sanitization
- CSRF protection

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Netlify
1. Build the project: `npm run build`
2. Deploy the `dist` folder to Netlify
3. Add environment variables in Netlify dashboard

## 📝 Environment Variables

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the SQL queries in `admin-sql-queries.sql` for database operations
- Review the Supabase documentation for backend features
- Check the component documentation for UI customization

## 🔄 Updates and Maintenance

- Regularly update dependencies: `npm update`
- Monitor Supabase usage and performance
- Backup database regularly
- Review and update security policies

---

Built with ❤️ using React, TypeScript, and Supabase.

```shell
pnpm i
```

**Start Preview**

```shell
pnpm run dev
```

**To build**

```shell
pnpm run build
```

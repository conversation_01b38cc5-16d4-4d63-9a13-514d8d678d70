-- =====================================================
-- COMPLETE DATABASE SCHEMA FOR NEWS WEBSITE
-- THE SACH PATRA - From Scratch Setup
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. ENUMS AND TYPES
-- =====================================================

-- User roles enum with dropdown support
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('guest', 'user', 'writer', 'editor', 'admin');
    END IF;
END $$;

-- Article categories enum
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'article_category') THEN
        CREATE TYPE article_category AS ENUM ('Politics', 'Tech', 'World', 'Sports', 'Entertainment', 'Business', 'Health', 'Science');
    END IF;
END $$;

-- Advertisement positions enum
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ad_position') THEN
        CREATE TYPE ad_position AS ENUM ('header', 'sidebar', 'footer', 'inline', 'popup');
    END IF;
END $$;

-- Setting types enum
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'setting_type') THEN
        CREATE TYPE setting_type AS ENUM ('text', 'boolean', 'number', 'json');
    END IF;
END $$;

-- =====================================================
-- 2. CORE TABLES
-- =====================================================

-- User Profiles Table (Main user management)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    avatar_url TEXT,
    role user_role DEFAULT 'user' NOT NULL,
    bio TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories Table (Dynamic category management)
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#6B7280', -- Hex color code
    icon VARCHAR(50), -- Icon name for UI
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    article_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Articles Table (Main content)
CREATE TABLE IF NOT EXISTS articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    summary TEXT NOT NULL,
    content TEXT NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    category VARCHAR(50) DEFAULT 'World', -- Backward compatibility
    image_url TEXT NOT NULL,
    author_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    published_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_featured BOOLEAN DEFAULT false,
    is_breaking BOOLEAN DEFAULT false,
    is_trending BOOLEAN DEFAULT false,
    is_published BOOLEAN DEFAULT true,
    views INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    shares INTEGER DEFAULT 0,
    reading_time INTEGER DEFAULT 5, -- Estimated reading time in minutes
    seo_title VARCHAR(255),
    seo_description TEXT,
    tags TEXT[], -- Array of tags
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comments Table
CREATE TABLE IF NOT EXISTS comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE, -- For nested comments
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT true,
    likes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookmarks Table
CREATE TABLE IF NOT EXISTS bookmarks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, article_id)
);

-- Newsletter Subscribers Table
CREATE TABLE IF NOT EXISTS newsletter_subscribers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}', -- Subscription preferences
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Advertisements Table
CREATE TABLE IF NOT EXISTS advertisements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    link_url TEXT NOT NULL,
    position ad_position DEFAULT 'sidebar',
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    clicks INTEGER DEFAULT 0,
    impressions INTEGER DEFAULT 0,
    budget DECIMAL(10,2),
    cost_per_click DECIMAL(5,2),
    created_by UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Website Settings Table
CREATE TABLE IF NOT EXISTS website_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type setting_type DEFAULT 'text',
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- Whether setting can be accessed by frontend
    updated_by UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. ADVANCED FEATURES TABLES
-- =====================================================

-- Newsletter Templates Table
CREATE TABLE IF NOT EXISTS newsletter_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    subject_template TEXT NOT NULL,
    html_template TEXT NOT NULL,
    is_default BOOLEAN DEFAULT false,
    created_by UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Newsletter Campaigns Table
CREATE TABLE IF NOT EXISTS newsletter_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    template_id UUID REFERENCES newsletter_templates(id) ON DELETE SET NULL,
    sent_at TIMESTAMP WITH TIME ZONE,
    recipients_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'draft', -- draft, scheduled, sending, sent, failed
    scheduled_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Article Views Tracking (for analytics)
CREATE TABLE IF NOT EXISTS article_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL, -- NULL for anonymous views
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Sessions (for analytics and security)
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Media Library (for file management)
CREATE TABLE IF NOT EXISTS media_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    alt_text TEXT,
    uploaded_by UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. INDEXES FOR PERFORMANCE
-- =====================================================

-- User profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_active ON user_profiles(is_active);

-- Articles indexes
CREATE INDEX IF NOT EXISTS idx_articles_author ON articles(author_id);
CREATE INDEX IF NOT EXISTS idx_articles_category ON articles(category_id);
CREATE INDEX IF NOT EXISTS idx_articles_published ON articles(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_articles_featured ON articles(is_featured);
CREATE INDEX IF NOT EXISTS idx_articles_breaking ON articles(is_breaking);
CREATE INDEX IF NOT EXISTS idx_articles_trending ON articles(is_trending);
CREATE INDEX IF NOT EXISTS idx_articles_status ON articles(is_published);
CREATE INDEX IF NOT EXISTS idx_articles_slug ON articles(slug);
CREATE INDEX IF NOT EXISTS idx_articles_views ON articles(views DESC);
CREATE INDEX IF NOT EXISTS idx_articles_tags ON articles USING GIN(tags);

-- Comments indexes
CREATE INDEX IF NOT EXISTS idx_comments_article ON comments(article_id);
CREATE INDEX IF NOT EXISTS idx_comments_user ON comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent ON comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_comments_approved ON comments(is_approved);

-- Bookmarks indexes
CREATE INDEX IF NOT EXISTS idx_bookmarks_user ON bookmarks(user_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_article ON bookmarks(article_id);

-- Newsletter indexes
CREATE INDEX IF NOT EXISTS idx_newsletter_email ON newsletter_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_newsletter_active ON newsletter_subscribers(is_active);

-- Advertisements indexes
CREATE INDEX IF NOT EXISTS idx_ads_position ON advertisements(position);
CREATE INDEX IF NOT EXISTS idx_ads_active ON advertisements(is_active);
CREATE INDEX IF NOT EXISTS idx_ads_dates ON advertisements(start_date, end_date);

-- Article views indexes
CREATE INDEX IF NOT EXISTS idx_article_views_article ON article_views(article_id);
CREATE INDEX IF NOT EXISTS idx_article_views_user ON article_views(user_id);
CREATE INDEX IF NOT EXISTS idx_article_views_date ON article_views(viewed_at);

-- =====================================================
-- 5. FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_articles_updated_at BEFORE UPDATE ON articles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_newsletter_subscribers_updated_at BEFORE UPDATE ON newsletter_subscribers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_advertisements_updated_at BEFORE UPDATE ON advertisements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_website_settings_updated_at BEFORE UPDATE ON website_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_newsletter_templates_updated_at BEFORE UPDATE ON newsletter_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_newsletter_campaigns_updated_at BEFORE UPDATE ON newsletter_campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_sessions_updated_at BEFORE UPDATE ON user_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_media_files_updated_at BEFORE UPDATE ON media_files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate article slug from title
CREATE OR REPLACE FUNCTION generate_article_slug()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.slug IS NULL OR NEW.slug = '' THEN
        NEW.slug := lower(regexp_replace(NEW.title, '[^a-zA-Z0-9\s]', '', 'g'));
        NEW.slug := regexp_replace(NEW.slug, '\s+', '-', 'g');
        NEW.slug := trim(both '-' from NEW.slug);

        -- Ensure uniqueness
        WHILE EXISTS (SELECT 1 FROM articles WHERE slug = NEW.slug AND id != COALESCE(NEW.id, uuid_generate_v4())) LOOP
            NEW.slug := NEW.slug || '-' || extract(epoch from now())::int;
        END LOOP;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER generate_article_slug_trigger BEFORE INSERT OR UPDATE ON articles FOR EACH ROW EXECUTE FUNCTION generate_article_slug();

-- Function to increment article views
CREATE OR REPLACE FUNCTION increment_article_views(article_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE articles SET views = views + 1 WHERE id = article_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to increment advertisement clicks
CREATE OR REPLACE FUNCTION increment_ad_clicks(ad_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE advertisements SET clicks = clicks + 1 WHERE id = ad_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to increment advertisement impressions
CREATE OR REPLACE FUNCTION increment_ad_impressions(ad_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE advertisements SET impressions = impressions + 1 WHERE id = ad_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to update category article count
CREATE OR REPLACE FUNCTION update_category_article_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update old category count
    IF TG_OP = 'UPDATE' AND OLD.category_id IS NOT NULL THEN
        UPDATE categories SET article_count = (
            SELECT COUNT(*) FROM articles WHERE category_id = OLD.category_id AND is_published = true
        ) WHERE id = OLD.category_id;
    END IF;

    -- Update new category count
    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') AND NEW.category_id IS NOT NULL THEN
        UPDATE categories SET article_count = (
            SELECT COUNT(*) FROM articles WHERE category_id = NEW.category_id AND is_published = true
        ) WHERE id = NEW.category_id;
    END IF;

    -- Handle delete
    IF TG_OP = 'DELETE' AND OLD.category_id IS NOT NULL THEN
        UPDATE categories SET article_count = (
            SELECT COUNT(*) FROM articles WHERE category_id = OLD.category_id AND is_published = true
        ) WHERE id = OLD.category_id;
        RETURN OLD;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_category_article_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON articles
    FOR EACH ROW EXECUTE FUNCTION update_category_article_count();

-- =====================================================
-- 6. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE newsletter_subscribers ENABLE ROW LEVEL SECURITY;
ALTER TABLE advertisements ENABLE ROW LEVEL SECURITY;
ALTER TABLE website_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE newsletter_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE newsletter_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view all profiles" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can manage all profiles" ON user_profiles FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('admin'))
);

-- Articles policies
CREATE POLICY "Anyone can view published articles" ON articles FOR SELECT USING (is_published = true);
CREATE POLICY "Authors can view own articles" ON articles FOR SELECT USING (auth.uid() = author_id);
CREATE POLICY "Writers can create articles" ON articles FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('writer', 'editor', 'admin'))
);
CREATE POLICY "Authors can update own articles" ON articles FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Editors can manage all articles" ON articles FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('editor', 'admin'))
);

-- Comments policies
CREATE POLICY "Anyone can view approved comments" ON comments FOR SELECT USING (is_approved = true);
CREATE POLICY "Users can create comments" ON comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own comments" ON comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Moderators can manage comments" ON comments FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('editor', 'admin'))
);

-- Bookmarks policies
CREATE POLICY "Users can manage own bookmarks" ON bookmarks FOR ALL USING (auth.uid() = user_id);

-- Newsletter policies
CREATE POLICY "Anyone can subscribe to newsletter" ON newsletter_subscribers FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can manage own subscription" ON newsletter_subscribers FOR ALL USING (
    auth.email() = email OR
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('admin'))
);

-- Advertisements policies
CREATE POLICY "Anyone can view active ads" ON advertisements FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage ads" ON advertisements FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('admin'))
);

-- Website settings policies
CREATE POLICY "Anyone can view public settings" ON website_settings FOR SELECT USING (is_public = true);
CREATE POLICY "Admins can manage settings" ON website_settings FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('admin'))
);

-- Categories policies
CREATE POLICY "Anyone can view active categories" ON categories FOR SELECT USING (is_active = true);
CREATE POLICY "Editors can manage categories" ON categories FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('editor', 'admin'))
);

-- Article views policies
CREATE POLICY "Users can create article views" ON article_views FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can view own article views" ON article_views FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Admins can view all article views" ON article_views FOR SELECT USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('admin'))
);

-- User sessions policies
CREATE POLICY "Users can manage own sessions" ON user_sessions FOR ALL USING (auth.uid() = user_id);

-- Media files policies
CREATE POLICY "Anyone can view public media" ON media_files FOR SELECT USING (is_public = true);
CREATE POLICY "Users can upload media" ON media_files FOR INSERT WITH CHECK (auth.uid() = uploaded_by);
CREATE POLICY "Users can manage own media" ON media_files FOR ALL USING (auth.uid() = uploaded_by);
CREATE POLICY "Admins can manage all media" ON media_files FOR ALL USING (
    EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role IN ('admin'))
);

-- =====================================================
-- 7. INITIAL DATA SETUP
-- =====================================================

-- Insert default categories
INSERT INTO categories (name, slug, description, color, icon, sort_order) VALUES
('Politics', 'politics', 'Political news and analysis', '#DC2626', 'Vote', 1),
('Technology', 'tech', 'Latest technology news and trends', '#2563EB', 'Laptop', 2),
('World', 'world', 'International news and global events', '#059669', 'Globe', 3),
('Sports', 'sports', 'Sports news and updates', '#EA580C', 'Trophy', 4),
('Entertainment', 'entertainment', 'Entertainment and celebrity news', '#9333EA', 'Film', 5),
('Business', 'business', 'Business and financial news', '#0891B2', 'TrendingUp', 6),
('Health', 'health', 'Health and medical news', '#16A34A', 'Heart', 7),
('Science', 'science', 'Scientific discoveries and research', '#7C3AED', 'Microscope', 8)
ON CONFLICT (slug) DO NOTHING;

-- Insert default website settings
INSERT INTO website_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_title', 'THE SACH PATRA', 'text', 'Website title', true),
('site_tagline', 'Truth in Every Story', 'text', 'Website tagline', true),
('site_description', 'Your trusted source for news and information', 'text', 'Website description for SEO', true),
('articles_per_page', '10', 'number', 'Number of articles per page', false),
('comments_enabled', 'true', 'boolean', 'Enable comments on articles', false),
('moderate_comments', 'true', 'boolean', 'Moderate comments before publishing', false),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', false),
('newsletter_enabled', 'true', 'boolean', 'Enable newsletter subscription', true),
('social_sharing_enabled', 'true', 'boolean', 'Enable social media sharing', true),
('analytics_enabled', 'true', 'boolean', 'Enable analytics tracking', false)
ON CONFLICT (setting_key) DO NOTHING;

-- Insert default newsletter template
INSERT INTO newsletter_templates (name, subject_template, html_template, is_default) VALUES
('Default Newsletter', 'Weekly News Digest - {{date}}',
'<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{subject}}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #D32F2F;">THE SACH PATRA</h1>
        <h2>{{subject}}</h2>
        <div>{{content}}</div>
        <hr>
        <p style="font-size: 12px; color: #666;">
            You received this email because you subscribed to our newsletter.
            <a href="{{unsubscribe_url}}">Unsubscribe</a>
        </p>
    </div>
</body>
</html>', true)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 8. ADMIN USER SETUP & ROLE MANAGEMENT
-- =====================================================

-- Function to create admin user (call this after user signs up)
CREATE OR REPLACE FUNCTION create_admin_user(
    user_id UUID,
    user_email TEXT,
    user_name TEXT DEFAULT NULL
)
RETURNS user_profiles AS $$
DECLARE
    new_profile user_profiles;
    username_val TEXT;
    full_name_val TEXT;
BEGIN
    -- Generate username from email if not provided
    username_val := COALESCE(user_name, split_part(user_email, '@', 1));
    full_name_val := COALESCE(user_name, split_part(user_email, '@', 1));

    -- Insert or update user profile with admin role
    INSERT INTO user_profiles (
        id,
        username,
        full_name,
        email,
        role,
        avatar_url,
        is_active,
        email_verified
    ) VALUES (
        user_id,
        username_val,
        full_name_val,
        user_email,
        'admin',
        'https://api.dicebear.com/7.x/avataaars/svg?seed=' || username_val,
        true,
        true
    )
    ON CONFLICT (id) DO UPDATE SET
        role = 'admin',
        email = user_email,
        is_active = true,
        email_verified = true,
        updated_at = NOW()
    RETURNING * INTO new_profile;

    RETURN new_profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to promote user to admin (for existing users)
CREATE OR REPLACE FUNCTION promote_to_admin(target_user_id UUID)
RETURNS user_profiles AS $$
DECLARE
    updated_profile user_profiles;
BEGIN
    UPDATE user_profiles
    SET role = 'admin', updated_at = NOW()
    WHERE id = target_user_id
    RETURNING * INTO updated_profile;

    IF updated_profile IS NULL THEN
        RAISE EXCEPTION 'User not found with ID: %', target_user_id;
    END IF;

    RETURN updated_profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle user registration (creates profile automatically)
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    username_val TEXT;
    full_name_val TEXT;
BEGIN
    -- Extract username and full name from metadata or email
    username_val := COALESCE(
        NEW.raw_user_meta_data->>'username',
        split_part(NEW.email, '@', 1)
    );

    full_name_val := COALESCE(
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1)
    );

    -- Create user profile
    INSERT INTO user_profiles (
        id,
        username,
        full_name,
        email,
        role,
        avatar_url,
        email_verified
    ) VALUES (
        NEW.id,
        username_val,
        full_name_val,
        NEW.email,
        'user', -- Default role
        COALESCE(
            NEW.raw_user_meta_data->>'avatar_url',
            'https://api.dicebear.com/7.x/avataaars/svg?seed=' || username_val
        ),
        NEW.email_confirmed_at IS NOT NULL
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration (if using Supabase Auth)
-- Note: This trigger should be created on auth.users table in Supabase
-- DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
-- CREATE TRIGGER on_auth_user_created
--     AFTER INSERT ON auth.users
--     FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- =====================================================
-- 9. UTILITY FUNCTIONS
-- =====================================================

-- Function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS TEXT AS $$
DECLARE
    user_role_val TEXT;
BEGIN
    SELECT role INTO user_role_val
    FROM user_profiles
    WHERE id = user_id;

    RETURN COALESCE(user_role_val, 'guest');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has admin access
CREATE OR REPLACE FUNCTION has_admin_access(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_profiles
        WHERE id = user_id AND role IN ('admin', 'editor')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get article statistics
CREATE OR REPLACE FUNCTION get_article_stats()
RETURNS JSON AS $$
DECLARE
    stats JSON;
BEGIN
    SELECT json_build_object(
        'total_articles', COUNT(*),
        'published_articles', COUNT(*) FILTER (WHERE is_published = true),
        'featured_articles', COUNT(*) FILTER (WHERE is_featured = true),
        'breaking_articles', COUNT(*) FILTER (WHERE is_breaking = true),
        'trending_articles', COUNT(*) FILTER (WHERE is_trending = true),
        'total_views', COALESCE(SUM(views), 0),
        'total_likes', COALESCE(SUM(likes), 0)
    ) INTO stats
    FROM articles;

    RETURN stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats()
RETURNS JSON AS $$
DECLARE
    stats JSON;
BEGIN
    SELECT json_build_object(
        'total_users', COUNT(*),
        'active_users', COUNT(*) FILTER (WHERE is_active = true),
        'admin_users', COUNT(*) FILTER (WHERE role = 'admin'),
        'editor_users', COUNT(*) FILTER (WHERE role = 'editor'),
        'writer_users', COUNT(*) FILTER (WHERE role = 'writer'),
        'regular_users', COUNT(*) FILTER (WHERE role = 'user')
    ) INTO stats
    FROM user_profiles;

    RETURN stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 10. SETUP COMPLETION MESSAGE
-- =====================================================

-- Display setup completion message
DO $$
BEGIN
    RAISE NOTICE '
    =====================================================
    ✅ DATABASE SCHEMA SETUP COMPLETE!
    =====================================================

    🎉 Your news website database is ready!

    📋 WHAT WAS CREATED:
    • All core tables (users, articles, comments, etc.)
    • Role-based permissions with dropdown support
    • Indexes for optimal performance
    • Triggers for automatic updates
    • Row Level Security policies
    • Initial categories and settings
    • Admin management functions

    🔧 NEXT STEPS:
    1. Create your first admin user by signing up
    2. Run: SELECT create_admin_user(''your-user-id'', ''<EMAIL>'');
    3. Or promote existing user: SELECT promote_to_admin(''user-id'');

    🎯 ROLE SYSTEM:
    • guest: Can view public content
    • user: Can comment and bookmark
    • writer: Can create articles
    • editor: Can manage articles and categories
    • admin: Full system access

    📊 FEATURES INCLUDED:
    • Article management with categories
    • User role management with dropdown
    • Comment system with moderation
    • Newsletter management
    • Advertisement system
    • Media file management
    • Analytics tracking
    • SEO optimization

    🔒 SECURITY:
    • Row Level Security enabled
    • Role-based access control
    • Input validation
    • SQL injection protection

    Happy publishing! 🚀
    ';
END $$;

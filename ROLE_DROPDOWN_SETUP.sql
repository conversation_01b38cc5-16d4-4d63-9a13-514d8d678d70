-- =====================================================
-- ROLE DROPDOWN SETUP FOR SUPABASE
-- Quick fix to get role dropdown working
-- =====================================================

-- Step 1: Create user role enum for dropdown support
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('guest', 'user', 'writer', 'editor', 'admin');
    END IF;
END $$;

-- Step 2: Add check constraint to existing role column for dropdown
ALTER TABLE user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_role_check;

ALTER TABLE user_profiles 
ADD CONSTRAINT user_profiles_role_check 
CHECK (role IN ('guest', 'user', 'writer', 'editor', 'admin'));

-- Step 3: Add comment to help Supabase UI recognize the constraint
COMMENT ON COLUMN user_profiles.role IS 'User role: guest, user, writer, editor, or admin';

-- Step 4: Update any existing invalid roles to 'user'
UPDATE user_profiles 
SET role = 'user' 
WHERE role NOT IN ('guest', 'user', 'writer', 'editor', 'admin');

-- Step 5: Create admin user function (generic - no hardcoded IDs)
CREATE OR REPLACE FUNCTION make_user_admin(target_email TEXT)
RETURNS user_profiles AS $$
DECLARE
    updated_profile user_profiles;
BEGIN
    UPDATE user_profiles 
    SET role = 'admin', updated_at = NOW()
    WHERE email = target_email OR username = target_email
    RETURNING * INTO updated_profile;
    
    IF updated_profile IS NULL THEN
        RAISE EXCEPTION 'User not found with email/username: %', target_email;
    END IF;
    
    RETURN updated_profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Create function to setup first admin (call after user registration)
CREATE OR REPLACE FUNCTION setup_first_admin()
RETURNS TEXT AS $$
DECLARE
    first_user user_profiles;
    result_message TEXT;
BEGIN
    -- Get the first user (oldest created_at)
    SELECT * INTO first_user
    FROM user_profiles 
    ORDER BY created_at ASC 
    LIMIT 1;
    
    IF first_user IS NULL THEN
        RETURN 'No users found. Please register first, then run this function.';
    END IF;
    
    -- Make them admin
    UPDATE user_profiles 
    SET role = 'admin', updated_at = NOW()
    WHERE id = first_user.id;
    
    result_message := format(
        'SUCCESS! User "%s" (%s) has been made admin. Role dropdown should now work in Supabase dashboard.',
        first_user.full_name,
        first_user.email
    );
    
    RETURN result_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 7: Show current role options and validation
SELECT 'ROLE VALIDATION CHECK:' as status;

-- Show the enum values
SELECT 'Valid role options:' as info, unnest(enum_range(NULL::user_role)) as valid_roles;

-- Show current users and their roles
SELECT 'CURRENT USERS:' as status;
SELECT 
    id,
    username,
    full_name,
    email,
    role,
    CASE 
        WHEN role IN ('admin', 'editor') THEN '✅ HAS ADMIN ACCESS' 
        ELSE '❌ NO ADMIN ACCESS' 
    END as admin_status,
    created_at
FROM user_profiles 
ORDER BY created_at ASC;

-- Step 8: Instructions
SELECT '
🎯 ROLE DROPDOWN SETUP COMPLETE!

📋 WHAT TO DO NEXT:

1. REFRESH SUPABASE DASHBOARD
   - Go to Table Editor → user_profiles
   - The role column should now show a dropdown with options:
     • guest, user, writer, editor, admin

2. MAKE YOURSELF ADMIN:
   Option A - Use the dropdown in Supabase dashboard
   Option B - Run this SQL command:
   SELECT make_user_admin(''<EMAIL>'');
   
   Option C - Make first registered user admin:
   SELECT setup_first_admin();

3. ROLE PERMISSIONS:
   • guest: View public content only
   • user: Comment, bookmark articles  
   • writer: Create and edit own articles
   • editor: Manage all articles, categories
   • admin: Full system access

4. VERIFY SETUP:
   - Check that role column shows dropdown in Supabase
   - Verify you can change roles using the dropdown
   - Test admin access in your application

✅ The role dropdown should now work perfectly!
' as instructions;

-- Step 9: Test the constraint (this should work)
DO $$
BEGIN
    -- This should succeed (valid role)
    PERFORM 1 WHERE 'admin'::text IN ('guest', 'user', 'writer', 'editor', 'admin');
    RAISE NOTICE '✅ Role constraint test passed!';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Role constraint test failed: %', SQLERRM;
END $$;

-- Final success message
SELECT '🚀 SETUP COMPLETE! Refresh Supabase dashboard to see the role dropdown.' as final_message;

# Complete Setup Guide - News Website Fixes

## Issues Fixed
1. **User Registration**: Users weren't getting profiles created in Supabase
2. **Admin User Management**: "Add User" button wasn't working
3. **Mock Data**: Home page was using mock data instead of real database data
4. **Missing Sample Data**: Database was empty, causing blank pages

## Complete Solution
1. **Database Trigger**: Automatic user profile creation on registration
2. **Admin User Creation**: Working dialog for creating new users
3. **Real Data Integration**: Home page now uses real database data
4. **Sample Data**: Comprehensive sample data for testing
5. **Row Level Security**: Proper data access policies

## Steps to Complete the Setup

### 1. Run the Database Updates
Go to your Supabase project dashboard:
1. Navigate to **SQL Editor**
2. Copy and paste the contents of `database-updates.sql`
3. Click **Run** to execute the SQL

### 2. Add Sample Data
1. In the same **SQL Editor**
2. Copy and paste the contents of `sample-data-comprehensive.sql`
3. Click **Run** to populate your database with sample data

### 3. Verify the Setup
After running the SQL, you can verify the setup:

```sql
-- Check if the trigger function exists
SELECT proname FROM pg_proc WHERE proname = 'handle_new_user';

-- Check if the trigger exists
SELECT tgname FROM pg_trigger WHERE tgname = 'on_auth_user_created';

-- Check RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('user_profiles', 'articles', 'comments', 'bookmarks');
```

### 4. Test All Functionality
1. **Registration**: Try registering a new user - profile should appear in Supabase
2. **Admin User Creation**: Login as admin and test the "Add User" button
3. **Home Page**: Check that real articles appear instead of mock data
4. **All Features**: Test articles, comments, bookmarks, etc.

## What the Fix Does

### Database Trigger Function
```sql
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, username, full_name, avatar_url, role)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || NEW.id::text),
        'user'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

This function:
- Automatically runs when a new user is created in `auth.users`
- Extracts user data from the registration metadata
- Creates a corresponding profile in `user_profiles`
- Sets default values if metadata is missing

### Frontend Improvements
The `useAuth` hook now:
- Checks for missing profiles and attempts to create them
- Handles username uniqueness validation
- Provides better error handling and user feedback
- Waits for profile creation after registration

## Row Level Security Policies
Added comprehensive RLS policies to ensure:
- Users can only access their own data
- Public data (like articles) is accessible to everyone
- Admin-only features are properly protected

## Troubleshooting

### If profiles still don't appear:
1. Check if the trigger was created successfully
2. Verify RLS policies are not blocking access
3. Check browser console for any errors
4. Try registering with a new email address

### Manual Profile Creation
If you need to create profiles for existing users:
```sql
INSERT INTO user_profiles (id, username, full_name, avatar_url, role)
SELECT 
    id,
    COALESCE(raw_user_meta_data->>'username', split_part(email, '@', 1)),
    COALESCE(raw_user_meta_data->>'full_name', split_part(email, '@', 1)),
    'https://api.dicebear.com/7.x/avataaars/svg?seed=' || id::text,
    'user'
FROM auth.users
WHERE id NOT IN (SELECT id FROM user_profiles);
```

## Environment Variables
Make sure your `.env` file has the correct Supabase credentials:
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## New Features Added

### 1. Admin User Creation
- **Location**: Admin Dashboard > User Management tab
- **Feature**: "Add User" button now opens a dialog to create new users
- **Functionality**: Creates user profiles that can later register with the provided email

### 2. Real Data Integration
- **Home Page**: Now uses real articles from database instead of mock data
- **Loading States**: Proper loading indicators while data is fetched
- **Fallbacks**: Graceful handling when no data is available

### 3. Sample Data
- **Articles**: 10+ sample articles across all categories
- **Users**: Sample user profiles with different roles
- **Comments**: Sample comments on articles
- **Advertisements**: Sample ads for testing
- **Settings**: Basic website configuration

## Admin Login
Use these credentials to access admin features:
- **Username**: admin
- **Email**: Create an account and update the role to 'admin' in Supabase

## Next Steps
After applying these fixes:
1. Run both SQL files in order (database-updates.sql, then sample-data-comprehensive.sql)
2. Test the registration flow thoroughly
3. Test admin user creation functionality
4. Verify that home page shows real articles
5. Check that all features work with real data

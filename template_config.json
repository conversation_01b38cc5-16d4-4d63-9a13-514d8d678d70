{"description": "- This is the DEFAULT template shadcn/ui, if the user's requirements do not specify a technology stack, development language, development tools, components, etc. This is a template for general web app, game, common application, tool development. - It includes a basic project structure with directories such as src/hooks, src/lib, and src/components. The src/components directory contains all the shadcn-ui components, with no additional installation commands required. If it's not necessary do not modify files outside the src directory.", "required_fields": [], "required_files": ["README.md", "src/App.tsx", "src/main.tsx", "src/pages/Index.tsx", "index.html"], "lang": "typescript", "framework": "react", "name": "shadcn/ui", "scene": "default_web_project"}
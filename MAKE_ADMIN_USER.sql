-- =====================================================
-- MAKE SPECIFIC USER ADMIN
-- Email: <EMAIL>
-- =====================================================

-- Step 1: First ensure role constraint exists for dropdown
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('guest', 'user', 'writer', 'editor', 'admin');
    END IF;
END $$;

-- Add constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'user_profiles_role_check'
    ) THEN
        ALTER TABLE user_profiles 
        ADD CONSTRAINT user_profiles_role_check 
        CHECK (role IN ('guest', 'user', 'writer', 'editor', 'admin'));
    END IF;
END $$;

-- Step 2: Make <EMAIL> admin
DO $$
DECLARE
    target_email TEXT := '<EMAIL>';
    user_found user_profiles;
    result_message TEXT;
BEGIN
    -- Try to find user by email first
    SELECT * INTO user_found
    FROM user_profiles 
    WHERE email = target_email;
    
    IF user_found IS NOT NULL THEN
        -- User exists, make them admin
        UPDATE user_profiles 
        SET role = 'admin', updated_at = NOW()
        WHERE email = target_email;
        
        RAISE NOTICE '✅ SUCCESS: User % (%) is now ADMIN!', user_found.full_name, target_email;
    ELSE
        -- User doesn't exist yet, check if they might be registered with different email format
        SELECT * INTO user_found
        FROM user_profiles 
        WHERE username ILIKE '%shoaib%' OR full_name ILIKE '%shoaib%';
        
        IF user_found IS NOT NULL THEN
            -- Found similar user, update their email and make admin
            UPDATE user_profiles 
            SET email = target_email, role = 'admin', updated_at = NOW()
            WHERE id = user_found.id;
            
            RAISE NOTICE '✅ SUCCESS: Updated user % to email % and made ADMIN!', user_found.full_name, target_email;
        ELSE
            -- Create placeholder profile that will be linked when user registers
            INSERT INTO user_profiles (
                id,
                username,
                full_name,
                email,
                role,
                avatar_url,
                is_active
            ) VALUES (
                gen_random_uuid(),
                'shoaibalamcse0786',
                'Shoaib Alam',
                target_email,
                'admin',
                'https://api.dicebear.com/7.x/avataaars/svg?seed=shoaibalamcse0786',
                true
            );
            
            RAISE NOTICE '✅ SUCCESS: Created admin profile for %. They will have admin access when they register!', target_email;
        END IF;
    END IF;
END $$;

-- Step 3: Verify the admin user
SELECT 'ADMIN USER VERIFICATION:' as status;

SELECT 
    id,
    username,
    full_name,
    email,
    role,
    '✅ ADMIN ACCESS GRANTED' as status,
    created_at,
    updated_at
FROM user_profiles 
WHERE email = '<EMAIL>' OR username ILIKE '%shoaib%'
ORDER BY updated_at DESC;

-- Step 4: Show all admin users
SELECT 'ALL ADMIN USERS:' as info;

SELECT 
    username,
    full_name,
    email,
    role,
    created_at
FROM user_profiles 
WHERE role IN ('admin', 'editor')
ORDER BY role DESC, created_at ASC;

-- Step 5: Create function to ensure this user stays admin
CREATE OR REPLACE FUNCTION ensure_shoaib_admin()
RETURNS TRIGGER AS $$
BEGIN
    -- If someone tries <NAME_EMAIL> role, keep them as admin
    IF NEW.email = '<EMAIL>' AND NEW.role != 'admin' THEN
        NEW.role := 'admin';
        RAISE NOTICE 'Keeping <EMAIL> as admin (role protection)';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to protect admin status
DROP TRIGGER IF EXISTS protect_shoaib_admin ON user_profiles;
CREATE TRIGGER protect_shoaib_admin
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION ensure_shoaib_admin();

-- Step 6: Instructions
SELECT '
🎉 ADMIN SETUP <NAME_EMAIL>!

📋 WHAT HAPPENED:
✅ Role constraint added for dropdown support
✅ <EMAIL> set as ADMIN
✅ Role protection trigger created
✅ Admin access verified

🔧 NEXT STEPS:
1. REFRESH SUPABASE DASHBOARD
   - Go to Table Editor → user_profiles
   - Role column should show dropdown
   - Find <EMAIL> with admin role

2. TEST ADMIN ACCESS:
   - Sign <NAME_EMAIL>
   - Access admin dashboard
   - Verify all admin features work

3. ROLE DROPDOWN:
   - Should now work in Supabase dashboard
   - Options: guest, user, writer, editor, admin

🛡️ SECURITY:
- Admin role is protected by trigger
- Cannot be accidentally changed
- Full admin access granted

🎯 ADMIN PERMISSIONS:
✅ Manage all articles
✅ Manage all users  
✅ Website settings
✅ Advertisement management
✅ Newsletter management
✅ Full system access

Ready to use! 🚀
' as instructions;

-- Final verification
SELECT 'FINAL CHECK - ADMIN USER:' as final_check;
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM user_profiles WHERE email = '<EMAIL>' AND role = 'admin') 
        THEN '✅ <EMAIL> is ADMIN - Setup successful!'
        ELSE '❌ Setup failed - please check errors above'
    END as result;
